/*
Theme Name: D30 (Geometry Dash Full Version)
Description: A theme for a Geometry Dash Full Version game website.
Version: 1.0
*/

body {
  font-family: sans-serif;
  margin: 0; /* Reset default body margins */
  background-color: #f0f0f0; /* Light background */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

header {
  background-color: #fff;
  padding: 20px;
  border-bottom: 1px solid #ccc;
}

.site-title {
  font-size: 24px;
  margin: 0;
}

.main-navigation ul {
  list-style: none;
  padding: 0;
  margin: 10px 0 0;
}

.main-navigation li {
  display: inline;
  margin-right: 20px;
}

.main-navigation a {
  text-decoration: none;
  color: #333;
}

.search-box {
  float: right;
}

footer {
  background-color: #333;
  color: #fff;
  padding: 20px;
  text-align: center;
  margin-top: 20px;
}

.game-frame-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.game-frame {
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  width: 80%; /* Adjust as needed */
  max-width: 800px; /* Adjust as needed */
}

.play-button {
  background-color: #ff9900; /* Orange */
  color: #fff;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.play-button:hover {
  background-color: #ffb347; /* Lighter orange */
}

.loading-screen {
  text-align: center;
  margin-top: 20px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #333;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}